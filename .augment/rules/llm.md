---
type: "always_apply"
---

# Project Architecture

This project is split into two parts `Drops.SQL` and `Drops.Relation`.

Drops.SQL is responsible for database schema introspection via:

- `Drops.SQL.Database` - behaviour and high-level API for introspecting database schemas. Defines a `introspect_table` callback that backends must implement
- `Drops.SQL.Database.table/2` - provides high-level API for retrieving table information with columns, primary keys, foreign keys and indices.
- `Drops.SQL.Postgres` - backend for PostgreSQL that `Database` uses
- `Drops.SQL.Sqlite` - backend for SQLite that `Database` uses
- `Drops.SQL.Compiler` - turns AST returned from `introspect_table` callback into a `Drops.SQL.Database.Table` struct - this compiler must provide actual database schema information without any mappings like converting db types to Ecto types

`Drops.Relation` is a high-level API for defining relations with Ecto schemas automatically inferred as `Struct` modules defined within a relation module. This is handled cia:

- `Drops.Relation.Schema` - represents a high-level relation schema that is built by translating `Drops.SQL.Database.Table` into a schema with fields
- `Drops.Relation.Compilers.SchemaCompiler` - takes an `Drops.SQL.Database.Table` and builds a `Drops.Relation.Schema`, uses `Drops.SQL.Types` protocol to translate database types to `Ecto` types and they are stored as `field.type` attribute, and database types remain as `field.meta.type`
- `Drops.SQL.Types.*` - db-specific modules for handling `to_ecto_type(column)` conversions and must return an Ecto type that will be set for a given field based on information from its corresponding column which is a `Drops.SQL.Database.Column` struct

# Testing setup and debugging

- Test suite uses migrations from `priv/repo` - whenever you change a migration you need to run `MIX_ENV=test mix ecto.reset`
- To inspect database columns it's best to rely on existing test and add IO.inspect to code and run tests to see the output instead of creating special ad-hoc scripts or using iEX
- Tests should always rely on tables created from migrations and never created tables manually via SQL in the `test do ... end` blocks or test setup blocks

# Refactoring Rules

- Do not try to keep backwards compatibility unless you are explicitly asked to do so
- Ensure all tests are passing
