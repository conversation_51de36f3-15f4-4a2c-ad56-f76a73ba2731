# Script to analyze migrations and compare column types between PostgreSQL and SQLite
# This script will run migrations, introspect the databases, and categorize columns

# Run dev setup to ensure databases are ready
Mix.Task.run("drops.relation.dev_setup")

# Start the repos
Application.ensure_all_started(:drops_relation)
Drops.Relation.Repos.start(Drops.Relation.Repos.Postgres)
Drops.Relation.Repos.start(Drops.Relation.Repos.Sqlite)

defmodule MigrationAnalyzer do
  @moduledoc """
  Analyzes migrations and compares column types between PostgreSQL and SQLite
  """

  def analyze do
    IO.puts("Analyzing migrations and comparing column types...")

    # Get all tables from both databases
    postgres_tables = get_all_tables(:postgres)
    sqlite_tables = get_all_tables(:sqlite)

    # Find common table names
    common_table_names = MapSet.intersection(
      MapSet.new(Map.keys(postgres_tables)),
      MapSet.new(Map.keys(sqlite_tables))
    )

    IO.puts("Found #{MapSet.size(common_table_names)} common tables")

    # Analyze columns for each common table
    {common_columns, postgres_specific, sqlite_specific} =
      analyze_columns(common_table_names, postgres_tables, sqlite_tables)

    # Print results
    print_analysis_results(common_columns, postgres_specific, sqlite_specific)

    # Generate migration content
    generate_migration_content(common_columns, postgres_specific, sqlite_specific)
  end

  defp get_all_tables(db_type) do
    repo = case db_type do
      :postgres -> Drops.Relation.Repos.Postgres
      :sqlite -> Drops.Relation.Repos.Sqlite
    end

    # Get all table names
    table_names = case db_type do
      :postgres ->
        query = """
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_type = 'BASE TABLE'
        AND table_name NOT LIKE 'schema_migrations'
        """
        result = Ecto.Adapters.SQL.query!(repo, query)
        Enum.map(result.rows, fn [name] -> String.to_atom(name) end)

      :sqlite ->
        query = """
        SELECT name
        FROM sqlite_master
        WHERE type = 'table'
        AND name NOT LIKE 'schema_migrations'
        AND name NOT LIKE 'sqlite_%'
        """
        result = Ecto.Adapters.SQL.query!(repo, query)
        Enum.map(result.rows, fn [name] -> String.to_atom(name) end)
    end

    # Get table details for each table
    tables = for table_name <- table_names, into: %{} do
      {:ok, table} = Drops.SQL.Database.table(Atom.to_string(table_name), repo)
      {table_name, table}
    end

    IO.puts("#{db_type}: Found #{length(table_names)} tables")
    tables
  end

  defp analyze_columns(common_table_names, postgres_tables, sqlite_tables) do
    common_columns = []
    postgres_specific = []
    sqlite_specific = []

    {common, pg_specific, sqlite_specific} =
      Enum.reduce(common_table_names, {common_columns, postgres_specific, sqlite_specific},
        fn table_name, {common_acc, pg_acc, sqlite_acc} ->
          pg_table = postgres_tables[table_name]
          sqlite_table = sqlite_tables[table_name]

          # Get columns from both tables
          pg_columns = Map.new(pg_table.columns, fn col -> {col.name, col} end)
          sqlite_columns = Map.new(sqlite_table.columns, fn col -> {col.name, col} end)

          # Find common column names
          common_col_names = MapSet.intersection(
            MapSet.new(Map.keys(pg_columns)),
            MapSet.new(Map.keys(sqlite_columns))
          )

          # Analyze each common column
          Enum.reduce(common_col_names, {common_acc, pg_acc, sqlite_acc},
            fn col_name, {c_acc, p_acc, s_acc} ->
              pg_col = pg_columns[col_name]
              sqlite_col = sqlite_columns[col_name]

              # Compare type and default
              if columns_equivalent?(pg_col, sqlite_col) do
                # Common column
                column_info = %{
                  name: col_name,
                  type: pg_col.type,
                  default: pg_col.meta.default,
                  null: pg_col.meta.null,
                  table: table_name
                }
                {[column_info | c_acc], p_acc, s_acc}
              else
                # Database-specific columns
                pg_info = %{
                  name: col_name,
                  type: pg_col.type,
                  default: pg_col.meta.default,
                  null: pg_col.meta.null,
                  table: table_name
                }
                sqlite_info = %{
                  name: col_name,
                  type: sqlite_col.type,
                  default: sqlite_col.meta.default,
                  null: sqlite_col.meta.null,
                  table: table_name
                }
                {c_acc, [pg_info | p_acc], [sqlite_info | s_acc]}
              end
            end)
        end)

    {common, pg_specific, sqlite_specific}
  end

  defp columns_equivalent?(pg_col, sqlite_col) do
    pg_col.type == sqlite_col.type and
    pg_col.meta.default == sqlite_col.meta.default
  end

  defp print_analysis_results(common_columns, postgres_specific, sqlite_specific) do
    IO.puts("\n=== ANALYSIS RESULTS ===")
    IO.puts("Common columns: #{length(common_columns)}")
    IO.puts("PostgreSQL-specific columns: #{length(postgres_specific)}")
    IO.puts("SQLite-specific columns: #{length(sqlite_specific)}")

    IO.puts("\n--- Common Columns ---")
    common_columns
    |> Enum.group_by(fn col -> {col.type, col.default} end)
    |> Enum.each(fn {{type, default}, cols} ->
      IO.puts("Type: #{inspect(type)}, Default: #{inspect(default)} (#{length(cols)} columns)")
    end)

    IO.puts("\n--- PostgreSQL-Specific Columns ---")
    postgres_specific
    |> Enum.group_by(fn col -> {col.type, col.default} end)
    |> Enum.each(fn {{type, default}, cols} ->
      IO.puts("Type: #{inspect(type)}, Default: #{inspect(default)} (#{length(cols)} columns)")
    end)

    IO.puts("\n--- SQLite-Specific Columns ---")
    sqlite_specific
    |> Enum.group_by(fn col -> {col.type, col.default} end)
    |> Enum.each(fn {{type, default}, cols} ->
      IO.puts("Type: #{inspect(type)}, Default: #{inspect(default)} (#{length(cols)} columns)")
    end)
  end

  defp generate_migration_content(common_columns, postgres_specific, sqlite_specific) do
    timestamp = DateTime.utc_now() |> DateTime.to_unix() |> to_string()

    # Generate common migration
    common_migration = generate_common_migration(common_columns, timestamp)
    File.write!("priv/repo/#{timestamp}_common_types.exs", common_migration)

    # Generate PostgreSQL-specific migration
    pg_migration = generate_postgres_migration(postgres_specific, timestamp)
    File.write!("priv/repo/postgres/#{timestamp}_custom_types.exs", pg_migration)

    # Generate SQLite-specific migration
    sqlite_migration = generate_sqlite_migration(sqlite_specific, timestamp)
    File.write!("priv/repo/sqlite/#{timestamp}_custom_types.exs", sqlite_migration)

    # Create symlinks for common migration
    File.ln_s!("../#{timestamp}_common_types.exs", "priv/repo/postgres/#{timestamp}_common_types.exs")
    File.ln_s!("../#{timestamp}_common_types.exs", "priv/repo/sqlite/#{timestamp}_common_types.exs")

    IO.puts("\nGenerated migration files:")
    IO.puts("- priv/repo/#{timestamp}_common_types.exs")
    IO.puts("- priv/repo/postgres/#{timestamp}_custom_types.exs")
    IO.puts("- priv/repo/sqlite/#{timestamp}_custom_types.exs")
    IO.puts("- Created symlinks for common migration")
  end

  defp generate_common_migration(common_columns, timestamp) do
    unique_types = common_columns
    |> Enum.group_by(fn col -> {col.type, col.default, col.null} end)
    |> Enum.map(fn {{type, default, null}, _cols} -> {type, default, null} end)
    |> Enum.uniq()

    fields = Enum.map_join(unique_types, "\n      ", fn {type, default, null} ->
      field_name = generate_field_name(type, default, null)
      options = generate_field_options(default, null)
      "add :#{field_name}, #{inspect(type)}#{options}"
    end)

    """
    defmodule Drops.Relation.Repos.CommonTypes do
      use Ecto.Migration

      def change do
        create table(:common_types) do
          #{fields}

          timestamps()
        end
      end
    end
    """
  end

  defp generate_postgres_migration(postgres_specific, timestamp) do
    unique_types = postgres_specific
    |> Enum.group_by(fn col -> {col.type, col.default, col.null} end)
    |> Enum.map(fn {{type, default, null}, _cols} -> {type, default, null} end)
    |> Enum.uniq()

    fields = Enum.map_join(unique_types, "\n      ", fn {type, default, null} ->
      field_name = generate_field_name(type, default, null)
      options = generate_field_options(default, null)
      "add :#{field_name}, #{inspect(type)}#{options}"
    end)

    """
    defmodule Drops.Relation.Repos.Postgres.Migrations.CustomTypes do
      use Ecto.Migration

      def change do
        create table(:custom_types) do
          #{fields}

          timestamps()
        end
      end
    end
    """
  end

  defp generate_sqlite_migration(sqlite_specific, timestamp) do
    unique_types = sqlite_specific
    |> Enum.group_by(fn col -> {col.type, col.default, col.null} end)
    |> Enum.map(fn {{type, default, null}, _cols} -> {type, default, null} end)
    |> Enum.uniq()

    fields = Enum.map_join(unique_types, "\n      ", fn {type, default, null} ->
      field_name = generate_field_name(type, default, null)
      options = generate_field_options(default, null)
      "add :#{field_name}, #{inspect(type)}#{options}"
    end)

    """
    defmodule Drops.Relation.Repos.Sqlite.Migrations.CustomTypes do
      use Ecto.Migration

      def change do
        create table(:custom_types) do
          #{fields}

          timestamps()
        end
      end
    end
    """
  end

  defp generate_field_name(type, default, null) do
    base_name = case type do
      :string -> "string"
      :integer -> "integer"
      :boolean -> "boolean"
      :binary -> "binary"
      :text -> "text"
      :float -> "float"
      :decimal -> "decimal"
      :date -> "date"
      :time -> "time"
      :naive_datetime -> "naive_datetime"
      :timestamptz -> "timestamptz"
      :binary_id -> "binary_id"
      :map -> "map"
      other -> to_string(other)
    end

    suffix = case {default, null} do
      {nil, true} -> "_nullable"
      {nil, false} -> "_required"
      {default_val, true} when not is_nil(default_val) -> "_default_nullable"
      {default_val, false} when not is_nil(default_val) -> "_default_required"
      _ -> ""
    end

    "#{base_name}#{suffix}"
  end

  defp generate_field_options(default, null) do
    options = []

    options = if default != nil do
      [", default: #{inspect(default)}" | options]
    else
      options
    end

    options = if null == false do
      [", null: false" | options]
    else
      options
    end

    Enum.join(options, "")
  end
end

# Run the analysis
MigrationAnalyzer.analyze()
