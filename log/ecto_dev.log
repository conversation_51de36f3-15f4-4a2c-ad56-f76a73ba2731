12:41:00.228 [debug] Schema cache miss for Elixir.Drops.Relation.Repos.Sqlite.users (not cached: {:file_read, :enoent})
11:45:01.897 [info] QUERY ERROR queue=2.8ms
SELECT table_name
FROM information_schema.tables
WHERE table_schema = 'public'
AND table_type = 'BASE TABLE'
AND table_name NOT LIKE 'schema_migrations'
 []
11:45:19.419 [info] QUERY OK db=1.4ms decode=1.0ms queue=2.0ms
SELECT table_name
FROM information_schema.tables
WHERE table_schema = 'public'
AND table_type = 'BASE TABLE'
AND table_name NOT LIKE 'schema_migrations'
 []
11:45:19.429 [info] QUERY OK db=3.7ms queue=1.3ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["metadata_test"]
11:45:19.430 [info] QUERY OK db=0.7ms queue=0.3ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["metadata_test"]
11:45:19.432 [info] QUERY OK db=1.1ms queue=0.3ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["metadata_test"]
11:45:19.433 [info] QUERY OK db=0.4ms queue=0.2ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["metadata_test"]
11:45:19.443 [info] QUERY OK db=2.3ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["binary_id_organizations"]
11:45:19.444 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["binary_id_organizations"]
11:45:19.445 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["binary_id_organizations"]
11:45:19.445 [info] QUERY OK db=0.3ms queue=0.2ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["binary_id_organizations"]
11:45:19.449 [info] QUERY OK db=3.1ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["binary_id_users"]
11:45:19.450 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["binary_id_users"]
11:45:19.451 [info] QUERY OK db=0.9ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["binary_id_users"]
11:45:19.451 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["binary_id_users"]
11:45:19.455 [info] QUERY OK db=2.8ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["binary_id_posts"]
11:45:19.456 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["binary_id_posts"]
11:45:19.457 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["binary_id_posts"]
11:45:19.458 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["binary_id_posts"]
11:45:19.460 [info] QUERY OK db=1.8ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["uuid_organizations"]
11:45:19.460 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["uuid_organizations"]
11:45:19.462 [info] QUERY OK db=0.7ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["uuid_organizations"]
11:45:19.462 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["uuid_organizations"]
11:45:19.465 [info] QUERY OK db=2.8ms queue=0.3ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["uuid_users"]
11:45:19.466 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["uuid_users"]
11:45:19.467 [info] QUERY OK db=0.9ms queue=0.3ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["uuid_users"]
11:45:19.468 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["uuid_users"]
11:45:19.471 [info] QUERY OK db=2.7ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["uuid_posts"]
11:45:19.471 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["uuid_posts"]
11:45:19.473 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["uuid_posts"]
11:45:19.473 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["uuid_posts"]
11:45:19.475 [info] QUERY OK db=1.7ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["users"]
11:45:19.476 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["users"]
11:45:19.477 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["users"]
11:45:19.477 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["users"]
11:45:19.481 [info] QUERY OK db=3.1ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["user_groups"]
11:45:19.481 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["user_groups"]
11:45:19.482 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["user_groups"]
11:45:19.483 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["user_groups"]
11:45:19.485 [info] QUERY OK db=1.7ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["groups"]
11:45:19.485 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["groups"]
11:45:19.487 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["groups"]
11:45:19.487 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["groups"]
11:45:19.489 [info] QUERY OK db=1.7ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["association_parents"]
11:45:19.490 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["association_parents"]
11:45:19.491 [info] QUERY OK db=0.7ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["association_parents"]
11:45:19.491 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["association_parents"]
11:45:19.494 [info] QUERY OK db=2.6ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["associations"]
11:45:19.495 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["associations"]
11:45:19.496 [info] QUERY OK db=0.7ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["associations"]
11:45:19.496 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["associations"]
11:45:19.499 [info] QUERY OK db=2.6ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["association_items"]
11:45:19.500 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["association_items"]
11:45:19.501 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["association_items"]
11:45:19.501 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["association_items"]
11:45:19.503 [info] QUERY OK db=1.7ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["basic_types"]
11:45:19.504 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["basic_types"]
11:45:19.505 [info] QUERY OK db=0.9ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["basic_types"]
11:45:19.506 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["basic_types"]
11:45:19.508 [info] QUERY OK db=1.7ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["custom_pk"]
11:45:19.508 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["custom_pk"]
11:45:19.509 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["custom_pk"]
11:45:19.510 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["custom_pk"]
11:45:19.512 [info] QUERY OK db=1.6ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["no_pk"]
11:45:19.512 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["no_pk"]
11:45:19.513 [info] QUERY OK db=0.7ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["no_pk"]
11:45:19.514 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["no_pk"]
11:45:19.516 [info] QUERY OK db=1.7ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["composite_pk"]
11:45:19.517 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["composite_pk"]
11:45:19.518 [info] QUERY OK db=0.8ms queue=0.1ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["composite_pk"]
11:45:19.518 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["composite_pk"]
11:45:19.520 [info] QUERY OK db=1.7ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["timestamps"]
11:45:19.521 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["timestamps"]
11:45:19.522 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["timestamps"]
11:45:19.522 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["timestamps"]
11:45:19.525 [info] QUERY OK db=2.5ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["posts"]
11:45:19.526 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["posts"]
11:45:19.527 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["posts"]
11:45:19.527 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["posts"]
11:45:19.529 [info] QUERY OK db=1.6ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["type_mapping_tests"]
11:45:19.530 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["type_mapping_tests"]
11:45:19.531 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["type_mapping_tests"]
11:45:19.531 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["type_mapping_tests"]
11:45:19.534 [info] QUERY OK db=1.9ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["postgres_types"]
11:45:19.534 [info] QUERY OK db=0.4ms queue=0.2ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["postgres_types"]
11:45:19.536 [info] QUERY OK db=1.0ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["postgres_types"]
11:45:19.536 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["postgres_types"]
11:45:19.539 [info] QUERY OK db=2.0ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["postgres_array_types"]
11:45:19.539 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["postgres_array_types"]
11:45:19.540 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["postgres_array_types"]
11:45:19.541 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["postgres_array_types"]
11:45:19.543 [info] QUERY OK db=1.7ms queue=0.3ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["postgres_geometric_types"]
11:45:19.544 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["postgres_geometric_types"]
11:45:19.545 [info] QUERY OK db=0.7ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["postgres_geometric_types"]
11:45:19.545 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["postgres_geometric_types"]
11:45:19.549 [info] QUERY OK db=3.1ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["special_cases"]
11:45:19.549 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["special_cases"]
11:45:19.550 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["special_cases"]
11:45:19.551 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["special_cases"]
11:45:19.553 [info] QUERY OK db=1.6ms queue=0.3ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["postgres_serial_aliases"]
11:45:19.553 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["postgres_serial_aliases"]
11:45:19.555 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["postgres_serial_aliases"]
11:45:19.555 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["postgres_serial_aliases"]
11:45:19.560 [info] QUERY OK db=1.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'schema_migrations'
AND name NOT LIKE 'sqlite_%'
 []
11:45:19.561 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(users) []
11:45:19.561 [info] QUERY OK db=0.0ms
PRAGMA index_list(users) []
11:45:19.561 [info] QUERY OK db=0.0ms
PRAGMA index_info(users_name_age_index) []
11:45:19.561 [info] QUERY OK db=0.0ms
PRAGMA index_info(users_name_index) []
11:45:19.561 [info] QUERY OK db=0.0ms
PRAGMA index_info(users_email_index) []
11:45:19.561 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["users"]
11:45:19.562 [info] QUERY OK db=0.0ms
PRAGMA table_info(users) []
11:45:19.562 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
11:45:19.562 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
11:45:19.562 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
11:45:19.563 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
11:45:19.563 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
11:45:19.563 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
11:45:19.564 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(groups) []
11:45:19.564 [info] QUERY OK db=0.0ms
PRAGMA index_list(groups) []
11:45:19.564 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["groups"]
11:45:19.565 [info] QUERY OK db=0.0ms
PRAGMA table_info(groups) []
11:45:19.565 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["groups"]
11:45:19.565 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["groups"]
11:45:19.565 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["groups"]
11:45:19.565 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["groups"]
11:45:19.565 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["groups"]
11:45:19.565 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(user_groups) []
11:45:19.565 [info] QUERY OK db=0.0ms
PRAGMA index_list(user_groups) []
11:45:19.565 [info] QUERY OK db=0.0ms
PRAGMA index_info(user_groups_user_id_group_id_index) []
11:45:19.565 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["user_groups"]
11:45:19.566 [info] QUERY OK db=0.0ms
PRAGMA table_info(user_groups) []
11:45:19.566 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["user_groups"]
11:45:19.566 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["user_groups"]
11:45:19.566 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["user_groups"]
11:45:19.566 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["user_groups"]
11:45:19.566 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["user_groups"]
11:45:19.566 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(association_parents) []
11:45:19.566 [info] QUERY OK db=0.0ms
PRAGMA index_list(association_parents) []
11:45:19.566 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["association_parents"]
11:45:19.567 [info] QUERY OK db=0.0ms
PRAGMA table_info(association_parents) []
11:45:19.567 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_parents"]
11:45:19.567 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_parents"]
11:45:19.567 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_parents"]
11:45:19.567 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_parents"]
11:45:19.567 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(associations) []
11:45:19.567 [info] QUERY OK db=0.0ms
PRAGMA index_list(associations) []
11:45:19.567 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["associations"]
11:45:19.567 [info] QUERY OK db=0.0ms
PRAGMA table_info(associations) []
11:45:19.567 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["associations"]
11:45:19.568 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["associations"]
11:45:19.568 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["associations"]
11:45:19.568 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["associations"]
11:45:19.568 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["associations"]
11:45:19.568 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(association_items) []
11:45:19.570 [info] QUERY OK db=0.0ms
PRAGMA index_list(association_items) []
11:45:19.570 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["association_items"]
11:45:19.570 [info] QUERY OK db=0.0ms
PRAGMA table_info(association_items) []
11:45:19.570 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_items"]
11:45:19.570 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_items"]
11:45:19.570 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_items"]
11:45:19.570 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_items"]
11:45:19.571 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_items"]
11:45:19.571 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(basic_types) []
11:45:19.571 [info] QUERY OK db=0.0ms
PRAGMA index_list(basic_types) []
11:45:19.571 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["basic_types"]
11:45:19.571 [info] QUERY OK db=0.0ms
PRAGMA table_info(basic_types) []
11:45:19.571 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:45:19.571 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:45:19.571 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:45:19.571 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:45:19.571 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:45:19.571 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:45:19.571 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:45:19.571 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:45:19.571 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:45:19.571 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(custom_pk) []
11:45:19.572 [info] QUERY OK db=0.0ms
PRAGMA index_list(custom_pk) []
11:45:19.572 [info] QUERY OK db=0.0ms
PRAGMA index_info(sqlite_autoindex_custom_pk_1) []
11:45:19.572 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["custom_pk"]
11:45:19.572 [info] QUERY OK db=0.0ms
PRAGMA table_info(custom_pk) []
11:45:19.572 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["custom_pk"]
11:45:19.572 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["custom_pk"]
11:45:19.572 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["custom_pk"]
11:45:19.572 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["custom_pk"]
11:45:19.572 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(no_pk) []
11:45:19.572 [info] QUERY OK db=0.0ms
PRAGMA index_list(no_pk) []
11:45:19.572 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["no_pk"]
11:45:19.573 [info] QUERY OK db=0.0ms
PRAGMA table_info(no_pk) []
11:45:19.573 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["no_pk"]
11:45:19.573 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["no_pk"]
11:45:19.573 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["no_pk"]
11:45:19.573 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["no_pk"]
11:45:19.577 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(composite_pk) []
11:45:19.577 [info] QUERY OK db=0.0ms
PRAGMA index_list(composite_pk) []
11:45:19.577 [info] QUERY OK db=0.0ms
PRAGMA index_info(sqlite_autoindex_composite_pk_1) []
11:45:19.577 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["composite_pk"]
11:45:19.577 [info] QUERY OK db=0.0ms
PRAGMA table_info(composite_pk) []
11:45:19.577 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["composite_pk"]
11:45:19.577 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["composite_pk"]
11:45:19.577 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["composite_pk"]
11:45:19.577 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["composite_pk"]
11:45:19.577 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["composite_pk"]
11:45:19.578 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(timestamps) []
11:45:19.578 [info] QUERY OK db=0.0ms
PRAGMA index_list(timestamps) []
11:45:19.578 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["timestamps"]
11:45:19.578 [info] QUERY OK db=0.0ms
PRAGMA table_info(timestamps) []
11:45:19.578 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["timestamps"]
11:45:19.578 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["timestamps"]
11:45:19.578 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["timestamps"]
11:45:19.578 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["timestamps"]
11:45:19.579 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(posts) []
11:45:19.579 [info] QUERY OK db=0.0ms
PRAGMA index_list(posts) []
11:45:19.579 [info] QUERY OK db=0.0ms
PRAGMA index_info(posts_title_index) []
11:45:19.579 [info] QUERY OK db=0.0ms
PRAGMA index_info(posts_published_index) []
11:45:19.579 [info] QUERY OK db=0.0ms
PRAGMA index_info(posts_user_id_index) []
11:45:19.579 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["posts"]
11:45:19.579 [info] QUERY OK db=0.0ms
PRAGMA table_info(posts) []
11:45:19.580 [info] QUERY OK db=0.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:45:19.580 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:45:19.580 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:45:19.580 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:45:19.580 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:45:19.580 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:45:19.580 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:45:19.580 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:45:19.580 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(type_mapping_tests) []
11:45:19.584 [info] QUERY OK db=0.0ms
PRAGMA index_list(type_mapping_tests) []
11:45:19.584 [info] QUERY OK db=0.0ms
PRAGMA index_info(type_mapping_tests_boolean_type_date_type_index) []
11:45:19.584 [info] QUERY OK db=0.0ms
PRAGMA index_info(type_mapping_tests_text_type_index) []
11:45:19.584 [info] QUERY OK db=0.0ms
PRAGMA index_info(type_mapping_tests_integer_type_index) []
11:45:19.584 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["type_mapping_tests"]
11:45:19.584 [info] QUERY OK db=0.0ms
PRAGMA table_info(type_mapping_tests) []
11:45:19.584 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.584 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.584 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.585 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.585 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.585 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.585 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.585 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.585 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.585 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.585 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.585 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.586 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.586 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.586 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.586 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.586 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.586 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.586 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.586 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.586 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(postgres_types) []
11:45:19.586 [info] QUERY OK db=0.0ms
PRAGMA index_list(postgres_types) []
11:45:19.587 [info] QUERY OK db=0.0ms
PRAGMA index_info(postgres_types_varchar_type_text_type_index) []
11:45:19.587 [info] QUERY OK db=0.0ms
PRAGMA index_info(postgres_types_uuid_type_index) []
11:45:19.587 [info] QUERY OK db=0.0ms
PRAGMA index_info(postgres_types_integer_type_index) []
11:45:19.587 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["postgres_types"]
11:45:19.587 [info] QUERY OK db=0.0ms
PRAGMA table_info(postgres_types) []
11:45:19.587 [info] QUERY OK db=0.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.591 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.591 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.591 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.591 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.591 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.591 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.591 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.592 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.592 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.592 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.592 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.592 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.592 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.592 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.592 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.592 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.592 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.592 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.593 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.593 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.593 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.593 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.593 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.593 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.593 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.593 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.593 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.593 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.593 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.593 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.593 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.594 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.594 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.594 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.598 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.598 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.598 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.598 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
